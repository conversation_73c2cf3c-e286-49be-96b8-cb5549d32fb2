flowchart TD
    A[GoGroupView 页面加载] --> B[componentWillMount]
    
    B --> C[获取基础参数<br/>instant_token, channel_type, is4ths]
    
    C --> D{同花顺渠道?}
    D -->|是| E{token有效?}
    E -->|无效| F[跳转同花顺登录]
    E -->|有效| G[继续处理]
    D -->|否| G
    
    G --> H[处理sd_token和特殊渠道]
    H --> I{token最终检查}
    I -->|无效| J[跳转登录]
    I -->|有效| K[componentDidMount]
    
    K --> L[获取业务参数<br/>groupName, backUrl等]
    
    L --> M{同花顺业务?}
    M -->|是| N[thscode映射获取groupName]
    M -->|否| O[直接使用groupName]
    
    N --> P[业务名称处理<br/>去除ForWeb后缀]
    O --> P
    
    P --> Q{登录需求判断}
    Q -->|免登录| R[直接执行Events.click]
    Q -->|凌志推送| S[打开bridge登录页面]
    Q -->|需要登录| T[调用_appInit]
    
    T --> U{QQ渠道?}
    U -->|是| V[QQ登录验证<br/>fetchToken4qq]
    U -->|否| W[普通登录验证<br/>调用appInit]
    
    V --> X{验证结果}
    X -->|成功| W
    X -->|失败| Y[登录失效提示]
    
    %% appInit 详细逻辑
    W --> W1[appInit开始]
    W1 --> W2[处理instant_token参数]
    W2 --> W3{QQ渠道特殊处理}
    W3 -->|是QQ渠道| W4[从sessionStorage获取token]
    W3 -->|否| W5[token有效性检查]
    W4 --> W5
    
    W5 --> W6{token状态}
    W6 -->|无效或空| W7[设置为notoken]
    W6 -->|与旧token相同| W8[清空token]
    W6 -->|新有效token| W9[设置Cookie保存]
    
    W7 --> W10[处理op_station和app_id]
    W8 --> W10
    W9 --> W10
    
    W10 --> W11{鸿蒙系统检查}
    W11 -->|是鸿蒙| W12[强制设置app_id为yjbweb]
    W11 -->|否| W13[保持原app_id]
    
    W12 --> W14{终端类型判断}
    W13 --> W14
    
    W14 -->|yjbjyd| W15[佣金宝2.0处理<br/>设置GoBackOnLoad]
    W14 -->|yjbwx| W16[微信端处理<br/>隐藏菜单设置]
    W14 -->|yjb3.0| W17[3.0客户端处理]
    W14 -->|yjbweb| W18[Web端处理<br/>页面刷新设置]
    W14 -->|其他| W19[其他终端处理]
    
    W15 --> W20[调用ssoInit]
    W16 --> W20
    W17 --> W20
    W18 --> W20
    W19 --> W20
    
    W20 --> W21{isWeekLogin?}
    W21 -->|是| W22[直接回调]
    W21 -->|否| W23[发起checklogin请求]
    
    W23 --> W24{请求结果}
    W24 -->|成功| W25[返回登录信息]
    W24 -->|失败| W26[返回false]
    
    W22 --> Z[获取登录信息]
    W25 --> Z
    W26 --> Z
    
    Z --> AA{配置检查}
    AA -->|存在| BB[使用现有配置]
    AA -->|不存在| CC[动态创建配置]
    
    BB --> DD{鸿蒙系统业务检查}
    CC --> DD
    DD -->|是且被禁用| EE[显示不支持提示]
    DD -->|否| FF[执行Events.click]
    
    R --> GG[Events处理]
    S --> GG
    FF --> GG
    
    GG --> HH[行为采集和页面设置]
    HH --> II[流程完成]
    
    F --> JJ[异常结束]
    J --> JJ
    Y --> JJ
    EE --> JJ
    
    style A fill:#e1f5fe
    style D fill:#fff3e0
    style M fill:#fff3e0
    style Q fill:#fff3e0
    style U fill:#fff3e0
    style W14 fill:#fff3e0
    style W21 fill:#fff3e0
    style AA fill:#fff3e0
    style DD fill:#fff3e0
    style GG fill:#e8f5e8
    style II fill:#e8f5e8
    style JJ fill:#ffebee