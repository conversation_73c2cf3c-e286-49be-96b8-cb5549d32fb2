<?php
namespace common\library;
use Yii;
use common\library\SessionFunc;
class SsoFunc {
    /**
     * 通过一次性token获取用户数据函数
     */
    public function getUserInfoByInstantToken($instant_token,$op_station)
    {
        $res = array('code'=>'-1');
        //第一步，通过instant_token获取durable_token
        $res_durable = $this->getDurableTokenByInstantToken($instant_token,$op_station);
        
        if($res_durable['code'] == 0) {
        	//第二步, 根据 durable_token 获取所有 sso 信息
            $par = array('durable_token'=>$res_durable['result']['durable_token']);
            $res_userinfo = $this->getUserInfoByDurableToken($par,$op_station);
            //print"<pre>";print_r($res_userinfo);exit;
            if($res_userinfo['code'] == 0){
            	$account = $res_userinfo['result']['account']; //注册sso时候的账号，暂时为普通资金账号和信用资金账号
            	$account_type = $res_userinfo['result']['auth_type'];
            	$passport_id = $res_userinfo['result']['passport_id'];
            	$cipher_token = $res_userinfo['result']['cipher_token'];
            	
            	//第三步, 根据 account 获取用户信息
            	$res_moreinfo = $this->userInfoQuery($account,$op_station,''); //支持用信用或普通方式来获取
				LogFunc::_log("debug",'行'.__LINE__.'--userInfoQuery响应数据:'.json_encode($res_moreinfo,true),'passport');
				if($res_moreinfo['code'] == 0){
					$res_userinfo['result']['userinfo_client_type'] = $res_moreinfo['result']['client_type'];
					$res_userinfo['result']['userinfo_client_status'] = $res_moreinfo['result']['client_status'];
					$res_userinfo['result']['userinfo_client_id'] = $res_moreinfo['result']['client_id'];
					$res_userinfo['result']['userinfo_client_name'] = $res_moreinfo['result']['client_name'];
					$res_userinfo['result']['userinfo_mobile'] = $res_moreinfo['result']['mobile'];
					$res_userinfo['result']['userinfo_tel'] = $res_moreinfo['result']['tel'];
					$res_userinfo['result']['branch_no'] = $res_moreinfo['result']['branch_no'];
					if((Yii::$app->params['env'] == 'dev' || Yii::$app->params['env'] == 'local') 
							&& !$res_userinfo['result']['branch_no']){
						$res_userinfo['result']['branch_no'] = 1000;
					}
						
					
					//第四步, 根据 $cipher_token和$account获取和检测密码
					/* $res_pwd = $this->getAndCheckPwd($cipher_token,$account,$account_type,$op_station);
					//print"<pre>";print_r($res_pwd);exit;
					if($res_pwd['code'] == 0){
						//$res_userinfo['result']['cipher_content'] = $res_pwd['result']['cipherContent'];//不存密码
						$res = $res_userinfo;
					}else if($res_pwd['code'] == -999){
						$res = array('code'=>-999, 'message'=>'获取用户密码失败');
					}else if($res_pwd['code'] == -1000){
						$res = array('code'=>-1000, 'message'=>'认证密码失败，可能是用户更改了密码');
					} */
					//********增加删除密码的相关代码
					$res = $res_userinfo;
				} else {
					$res = array('code'=>-998, 'message'=>'获取用户信息失败');
				}           	
            } elseif ($res_userinfo['code'] != 0 	// 获取数据错误
                || (array)$res_userinfo['result']['expired_time'] <= date('Y-m-d')) {	// 检查是否过期
                $res = array('code'=>-997, 'message'=>'durable_token无效，获取sso信息失败');
            }
        }else{
            $res = array('code'=>-996, 'message'=>'instant_token无效，获取durable失败');
        }
        LogFunc::_log("debug",'行'.__LINE__.'--getUserInfoByInstantToken的结果是:'.LogFunc::remove_password(json_encode($res,true)),'page');
        return $res;
    }

    /*
     * 通过一次token换取持久token
     */
    public function getDurableTokenByInstantToken($instant_token,$op_station)
    {
        $res = array('code'=>'-1');
        $params = array(
        	'commonFunc'=>true,
        	'act'=>'checkInstantToken', 
        	'instant_token'=>$instant_token,
        	'op_station'=>$op_station
        );
        $res = ApiFunc::api_curl($params, 'checkInstantToken--行：'.__LINE__);
        $res=json_decode($res, true);
        return $res;
    }
    
    /*
     * 通过持久token获取用户信息
     */
    public function getUserInfoByDurableToken($params,$op_station){
        $params = array(
        	'commonFunc'=>true,
        	'act'=>'findDurableTokenInfo', 
        	'durable_token'=>$params['durable_token'],
        	'op_station'=>$op_station
        );
        $res = ApiFunc::api_curl($params, 'findDurableTokenInfo--行：'.__LINE__);
        $res = json_decode($res, true);
        return $res;
    }
    
    /*
     * 查询账户信息
     */
    public function userInfoQuery($account,$op_station, $clientId)
    {
    	$params = array(
    		'commonFunc'=>true,
    		'act'=>'userInfoQuery', 
    		'op_station'=>$op_station,
    	);

		if($clientId){
			$params['client_id'] = $clientId;
		} else {
			$params['fund_account'] = $account;
		}

		LogFunc::_log("debug",'行'.__LINE__.'--userInfoQuery请求参数:'.json_encode($params,true),'passport');
    	$res = json_decode(ApiFunc::api_curl($params,'userInfoQuery--行:'.__LINE__), true);
    	LogFunc::_log("debug",'行'.__LINE__.'--userInfoQuery原始响应:'.json_encode($res,true),'passport');
    	return $res;
    }
    
    //根据 $cipher_token和$account获取和检测密码
    public function getAndCheckPwd($cipher_token,$account,$account_type,$op_station)
    {
    	$params = array(
    		'commonFunc'=>true,
    		'act'=>'getPassword',
    		'cipher_token'=>$cipher_token,
    		'op_station'=>$op_station
    	);
    	$res_pwd = json_decode(ApiFunc::api_curl($params,'getPassword用户密码--行:'.__LINE__), true);
    	//print_r("<pre>");print_r($res_pwd);exit;
    	
    	if($res_pwd['code'] == 0){
    		$pwd = $res_pwd['result']['cipherContent'];
    		$param = array(
    			'commonFunc'=>true,
    			"act" => "authenticate",
    			"accountType" => 1,
    			"accountId" => $account,
    			"password" => $pwd,
    			"op_station"=>$op_station
    		);
    		$ret_check = json_decode(ApiFunc::api_curl($param,'authenticate密码--行:'.__LINE__), true);
    		//print_r("<pre>");print_r($res_pwd);exit;
    		if($ret_check['code'] == 0){
    			$res = $res_pwd;
    		}else{
    			$res = array('code'=>-1000,'result'=>'认证密码失败');
    		}
    	}else{
    		$res = array('code'=>-999,'result'=>'获取密码失败');
    	}
    	return $res;
    }


	/**
	 * 通过一次性token获取用户数据函数 V2 注:获取用户信息及模板编号非框架通用需求,这里重写仅为方便迭代,建议各业务模块内部实现userInfoQuery功能.
	 * $auth_lv 为业务配置,用于用户权限级别的判断
	 * add by  JinYC
	 */
	public function 	getUserInfoByInstantTokenV2($instant_token,$op_station,$auth_lv){
		$res = array('code'=>'-1');
		/**
		 * SAS服务支持直接通过一次性token换取durable_token
		 */
		$res_userinfo = $this->getSessionInfoByInstantTokenV2($instant_token,$auth_lv);
		if($res_userinfo['code'] == 0) {
			$account = $res_userinfo['result']['account']; //注册sso时候的账号，暂时为普通资金账号和信用资金账号
			
			
			/**
			*使用clientId获取用户信息
			*/
			$clientId = $res_userinfo['result']['clientId'];
			//$account_type = $res_userinfo['result']['accountType'];
			$passport_id = $res_userinfo['result']['passportId'];
			//$cipher_token = $res_userinfo['result']['cipherToken'];
			$sso_session = unserialize(base64_decode(SessionFunc::readSession('sso_message')));
			LogFunc::_log("debug",'行'.__LINE__.'--readSession:'.json_encode($sso_session,true),'passport');
			LogFunc::_log("debug",'行'.__LINE__.'--getUserInfoByInstantTokenV2:'.json_encode($res_userinfo,true),'passport');
			$res_moreinfo = $this->userInfoQuery($account,$op_station, $clientId); //支持用信用或普通方式来获取
			LogFunc::_log("debug",'行'.__LINE__.'--userInfoQuery响应数据:'.json_encode($res_moreinfo,true),'passport');
			if($res_moreinfo['code'] == 0){
				$res_userinfo['result']['userinfo_client_type'] = $res_moreinfo['result']['client_type'];
				$res_userinfo['result']['userinfo_client_status'] = $res_moreinfo['result']['client_status'];
				$res_userinfo['result']['userinfo_client_id'] = $res_moreinfo['result']['client_id'];
				$res_userinfo['result']['userinfo_client_name'] = $res_moreinfo['result']['client_name'];
				$res_userinfo['result']['userinfo_mobile'] = $res_moreinfo['result']['mobile'];
				$res_userinfo['result']['userinfo_tel'] = $res_moreinfo['result']['tel'];
				$res_userinfo['result']['userinfo_gender'] = $res_moreinfo['result']['gender'];
				$res_userinfo['result']['userinfo_is_organ_client'] = $res_moreinfo['result']['is_organ_client'];
				$res_userinfo['result']['branch_no'] = $res_moreinfo['result']['branch_no'];
				if((Yii::$app->params['env'] == 'dev' || Yii::$app->params['env'] == 'local')
					&& !$res_userinfo['result']['branch_no']){
					$res_userinfo['result']['branch_no'] = 62;
				}
				$res = $res_userinfo;

			} else if ($res_moreinfo['code'] == 2020048) {
				$message = isset($res_moreinfo['message']) ? $res_moreinfo['message'] : '非主资金账号';
				$res = array('code'=>-990, 'message'=>$message);
			} else if ($res_moreinfo['code'] == 2020049) {
				$message = isset($res_moreinfo['message']) ? $res_moreinfo['message'] : '不支持的委托方式';
				$res = array('code'=>-991, 'message'=>$message);
			} else {
				$res = array('code'=>-998, 'message'=>'获取用户信息失败');
			}
		}else{
			$res = array('code'=>-996, 'message'=>'instant_token无效，获取durable失败');
		}
		LogFunc::_log("debug",'行'.__LINE__.'--getUserInfoByInstantTokenV2:'.LogFunc::remove_password(json_encode($res,true)),'page');
		return $res;
	}
	/*
	 * 通过一次token换取Session  注:建议使用
	 * add by  JinYC
	 */
	public function getSessionInfoByInstantTokenV2($instant_token,$auth_lv)
	{
		$res = array('code'=>'-1');
		$params = array(
			'commonFunc'=>true,
			'act'=>'session_infoQuery',
			'instant_token'=>$instant_token,
			'auth_lv'=>$auth_lv
		);
		$res = ApiFunc::api_curl($params, 'getSessionInfoByInstantTokenV2--行：'.__LINE__);
		$res=json_decode($res, true);
		return $res;
	}
	/*
     * 通过一次token换取持久token  注:建议废弃,并使用:getSessionInfoByInstantTokenV2
	 *  add by  JinYC
     */
	public function getDurableTokenByInstantTokenV2($instant_token,$auth_lv)
	{
		$res = array('code'=>'-1');
		$params = array(
			'commonFunc'=>true,
			'act'=>'instant_tokenCheck',
			'instant_token'=>$instant_token,
			'auth_lv'=>$auth_lv
		);
		$res = ApiFunc::api_curl($params, 'checkInstantToken--行：'.__LINE__);
		$res=json_decode($res, true);
		return $res;
	}
//	/*
//     * 通过持久token获取用户信息  注:建议废弃,并使用:getSessionInfoByInstantTokenV2
//     * add by  JinYC
//     */
//	public function getUserInfoByDurableTokenV2($params){
//		$params = array(
//			'commonFunc'=>true,
//			'act'=>'findDurableTokenInfo',
//			'durable_token'=>$params['durable_token']
//		);
//		$res = ApiFunc::api_curl($params, 'findDurableTokenInfo--行：'.__LINE__);
//		$res = json_decode($res, true);
//		return $res;
//	}
	/**
	 * 根据 $cipher_token获取密码 注: 如使用V2服务,则建议废弃getAndCheckPwd方法,不再使用authenticate接口检测密码正确
	 * add by  JinYC
	 */
	public function getPwdV2($cipher_token)
	{
		$params = array(
			'commonFunc'=>true,
			'act'=>'cipher_tokenPasswordQuery',
			'cipher_token'=>$cipher_token,
		);
		$res_pwd = json_decode(ApiFunc::api_curl($params,'cipher_tokenPasswordQuery--行:'.__LINE__), true);
		/**
		 *  要对密码token过期情况做特殊处理
		 */
		if($res_pwd['code'] == 0){
			$res = $res_pwd;
		}else{
			$res = array('code'=>-999,'result'=>'获取密码失败');
		}
		return $res;
	}
}
